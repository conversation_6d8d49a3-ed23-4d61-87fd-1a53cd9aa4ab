// tRPC客户端配置
import { createTRPCNuxtClient, httpBatchLink, httpLink } from 'trpc-nuxt/client'
import { isNonJsonSerializable, splitLink } from '@trpc/client'
import superjson from 'superjson'
import type { AppRouter } from '~/server/trpc/router'

export default defineNuxtPlugin(() => {
  if (import.meta.client) {
    try {
      // 创建tRPC客户端
      const trpc = createTRPCNuxtClient<AppRouter>({
        links: [
          splitLink({
            condition: (op) => isNonJsonSerializable(op.input),
            true: httpLink({
              url: '/api/trpc',
              transformer: superjson,
            }),
            false: httpBatchLink({
              url: '/api/trpc',
              transformer: superjson,
            }),
          }),
        ],
      })

      // 确保客户端正确初始化
      if (!trpc) {
        console.error('tRPC客户端初始化失败')
        throw new Error('tRPC客户端初始化失败')
      }

      console.log('tRPC客户端初始化成功')

      return {
        provide: {
          trpc,
        },
      }
    } catch (error) {
      console.error('tRPC插件初始化错误:', error)
      throw error
    }
  }
})
